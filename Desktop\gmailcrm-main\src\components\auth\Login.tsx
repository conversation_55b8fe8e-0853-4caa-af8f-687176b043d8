
import React, { useState, FormEvent, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Mail, Key } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { resendConfirmationEmail } from '@/services/supabaseAuthService';
import { confirmEmail } from '@/services/authService';

// Import shared components
import AuthLayout from './AuthLayout';
import GoogleButton from './GoogleButton';
import PasswordInput from './PasswordInput';
import AuthDivider from './AuthDivider';

const Login = () => {
  const { login, loginWithGoogle, isAuthenticated, isLoading, error } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [email, setEmail] = useState(location.state?.email || '');
  const [password, setPassword] = useState('');
  const [formError, setFormError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(
    location.state?.message || null
  );
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [isEmailNotConfirmed, setIsEmailNotConfirmed] = useState(false);
  const [isResendingEmail, setIsResendingEmail] = useState(false);

  // Get the redirect path from location state, URL params, or default to home
  const searchParams = new URLSearchParams(location.search);
  const returnTo = searchParams.get('returnTo');
  const connectGmail = searchParams.get('connectGmail') === 'true';
  const from = returnTo || location.state?.from?.pathname || '/dashboard';

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      if (connectGmail) {
        // Add a flag to trigger Gmail connection after redirect
        navigate(from, {
          replace: true,
          state: { connectGmail: true }
        });
      } else {
        navigate(from, { replace: true });
      }
    }
  }, [isAuthenticated, navigate, from, connectGmail]);

  // Load remembered email if available
  useEffect(() => {
    const rememberedEmail = localStorage.getItem('rememberedEmail');
    if (rememberedEmail) {
      setEmail(rememberedEmail);
      setRememberMe(true);
    }
  }, []);

  const handleGoogleSignIn = async () => {
    setFormError(null);
    setIsEmailNotConfirmed(false);

    try {
      await loginWithGoogle();
      // Navigation will happen in the useEffect above
    } catch (error) {
      // Error is handled by the auth context
    }
  };

  // Function to handle resending confirmation email
  const handleResendConfirmation = async () => {
    if (!email) {
      setFormError('Please enter your email address');
      return;
    }

    try {
      setIsResendingEmail(true);
      await resendConfirmationEmail(email);
      setSuccessMessage('Confirmation email sent! Please check your inbox and follow the instructions to confirm your email.');
      setFormError(null);
    } catch (error) {
      if (error instanceof Error) {
        setFormError(error.message);
      } else {
        setFormError('Failed to resend confirmation email. Please try again.');
      }
    } finally {
      setIsResendingEmail(false);
    }
  };

  // Function to handle manual email confirmation (development only)
  const handleManualConfirmation = async () => {
    if (!email) {
      setFormError('Please enter your email address');
      return;
    }

    try {
      setIsResendingEmail(true);
      await confirmEmail(email);
      setSuccessMessage('Email confirmed successfully! You can now log in.');
      setFormError(null);
      setIsEmailNotConfirmed(false);
    } catch (error) {
      if (error instanceof Error) {
        setFormError(error.message);
      } else {
        setFormError('Failed to confirm email. Please try again.');
      }
    } finally {
      setIsResendingEmail(false);
    }
  };

  const handleEmailSignIn = async (e: FormEvent) => {
    e.preventDefault();
    setFormError(null);
    setSuccessMessage(null);
    setIsEmailNotConfirmed(false);

    // Basic validation
    if (!email) {
      setFormError('Email is required');
      return;
    }

    if (!password) {
      setFormError('Password is required');
      return;
    }

    try {
      // Store email in localStorage if remember me is checked
      if (rememberMe) {
        localStorage.setItem('rememberedEmail', email);
      } else {
        localStorage.removeItem('rememberedEmail');
      }

      console.log('Attempting to log in with email:', email);
      console.log('Password length:', password.length);

      // Try login through our auth service
      try {
        console.log('Attempting to log in through auth service...');
        await login(email, password);
        console.log('Login successful');
        // Navigation will happen in the useEffect above
      } catch (loginError) {
        console.error('Login through auth service failed:', loginError);

        // Try direct Supabase login as a fallback
        try {
          console.log('Trying direct Supabase login as fallback...');
          const { supabase } = await import('@/services/supabase');

          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password
          });

          if (error) {
            console.error('Direct Supabase login error:', error);
            throw new Error(`Direct login failed: ${error.message}`);
          }

          if (!data || !data.user || !data.session) {
            throw new Error('Missing user data from Supabase');
          }

          console.log('Direct Supabase login successful:', data.user.id);

          // Get user data from the users table
          const { data: userData } = await supabase
            .from('users')
            .select('name')
            .eq('id', data.user.id)
            .single();

          // Set user in auth context
          const user = {
            id: data.user.id,
            email: data.user.email || email,
            name: userData?.name || data.user.user_metadata?.name || email.split('@')[0]
          };

          // Store token and user data
          localStorage.setItem('token', data.session.access_token);
          localStorage.setItem('user', JSON.stringify(user));

          // Reload the page to refresh the auth state
          window.location.href = '/';
        } catch (directError) {
          console.error('All login attempts failed:', directError);
          throw loginError; // Throw the original error for consistent error handling
        }
      }
    } catch (error) {
      console.error('Login error in component:', error);

      // Handle specific error messages
      if (error instanceof Error) {
        if (error.message.includes('network') || error.message.includes('connect') || error.message.includes('fetch')) {
          setFormError('Network error: Please check your internet connection and try again.');
        } else if (error.message.includes('credentials') || error.message.includes('password') || error.message.includes('Invalid')) {
          setFormError('Invalid email or password. Please try again.');
        } else if (error.message.includes('Email not confirmed') || error.message.includes('not confirmed') || error.message.includes('confirmation')) {
          setFormError(error.message);
          setIsEmailNotConfirmed(true);
        } else {
          setFormError(error.message);
        }
      } else {
        setFormError('An unexpected error occurred. Please try again later.');
      }
    }
  };

  // Email confirmation actions for when email is not confirmed
  const emailConfirmationActions = isEmailNotConfirmed && (
    <div className="mt-2 flex flex-col gap-2">
      <Button
        variant="outline"
        size="sm"
        className="mt-2 flex items-center gap-1 transition-all duration-300"
        onClick={handleResendConfirmation}
        disabled={isResendingEmail}
      >
        <Mail className="h-3 w-3" />
        {isResendingEmail ? 'Sending...' : 'Resend confirmation email'}
      </Button>

      {/* Development only button - remove in production */}
      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-1 transition-all duration-300"
        onClick={handleManualConfirmation}
        disabled={isResendingEmail}
      >
        <Key className="h-3 w-3" />
        {isResendingEmail ? 'Processing...' : 'Confirm email (Dev only)'}
      </Button>
    </div>
  );

  // Footer content
  const footerContent = (
    <>
      <p className="text-center text-sm text-muted-foreground">
        Don't have an account? <Link to="/signup" className="text-primary hover:underline font-medium transition-colors duration-200">Sign up</Link>
      </p>
      <p className="text-center text-xs text-muted-foreground mt-2">
        Demo credentials: <EMAIL> / password
      </p>
    </>
  );

  return (
    <AuthLayout
      title="Welcome Back"
      description="Sign in to manage your leads directly from Gmail"
      error={formError || error}
      successMessage={successMessage}
      footer={footerContent}
    >
      <div className="space-y-4">
        <div className="py-2">
          <GoogleButton
            onClick={handleGoogleSignIn}
            isLoading={isLoading}
            label="Sign in with Google"
          />
        </div>

        <AuthDivider />

        <form onSubmit={handleEmailSignIn} className="space-y-4">
          <div className="space-y-3">
            <div className="relative">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
                className="border-muted-foreground/20 focus:border-primary transition-all duration-300"
                required
              />
            </div>

            <PasswordInput
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isLoading}
              required
              id="password"
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember-me"
                checked={rememberMe}
                onCheckedChange={(checked) => setRememberMe(checked === true)}
                className="data-[state=checked]:bg-primary data-[state=checked]:border-primary transition-all duration-200"
              />
              <Label
                htmlFor="remember-me"
                className="text-sm font-medium leading-none cursor-pointer select-none"
              >
                Remember me
              </Label>
            </div>
            <Link
              to="/forgot-password"
              className="text-sm font-medium text-primary hover:text-primary/80 transition-colors duration-200"
            >
              Forgot password?
            </Link>
          </div>

          {isEmailNotConfirmed && emailConfirmationActions}

          <Button
            type="submit"
            className="w-full bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-300"
            disabled={isLoading}
          >
            {isLoading ? 'Signing in...' : 'Sign in'}
          </Button>
        </form>
      </div>
    </AuthLayout>
  );
};

export default Login;
