
import React from 'react';
import { Mail, Phone, MoreHorizontal, GripVertical } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { LeadItem } from './PipelineStage';
import { Draggable } from '@hello-pangea/dnd';

interface LeadProps {
  lead: LeadItem;
  index: number;
}

const LeadCard = ({ lead, index }: LeadProps) => {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('');
  };

  const getStatusClass = () => {
    if (lead.status === 'won') return 'tag tag-won';
    if (lead.status === 'lost') return 'tag tag-lost';
    return '';
  };

  return (
    <Draggable draggableId={lead.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`pipeline-card group hover:border-primary/20 ${
            snapshot.isDragging ? 'shadow-lg rotate-2 bg-white border-primary/40' : ''
          }`}
        >
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center gap-2">
              <Avatar className="h-7 w-7 border-2 border-primary/10">
                <AvatarFallback className="text-xs bg-primary/5 text-primary">
                  {getInitials(lead.name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h4 className="font-medium text-sm">{lead.name}</h4>
                <p className="text-xs text-muted-foreground">{lead.company}</p>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <div
                {...provided.dragHandleProps}
                className="text-muted-foreground hover:text-foreground opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-sm hover:bg-muted/50 cursor-grab active:cursor-grabbing"
              >
                <GripVertical className="h-4 w-4" />
              </div>
              <button className="text-muted-foreground hover:text-foreground opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-sm hover:bg-muted/50">
                <MoreHorizontal className="h-4 w-4" />
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between mt-3 text-xs border-t border-border/40 pt-2">
            <div className="font-medium text-primary">{lead.value}</div>
            <div className="text-muted-foreground">{lead.lastContact}</div>
          </div>

          <div className="flex justify-between mt-3">
            <div className="flex gap-1">
              <button className="p-1 rounded-sm hover:bg-muted/70 text-muted-foreground hover:text-primary transition-colors">
                <Mail className="h-3.5 w-3.5" />
              </button>
              <button className="p-1 rounded-sm hover:bg-muted/70 text-muted-foreground hover:text-primary transition-colors">
                <Phone className="h-3.5 w-3.5" />
              </button>
            </div>
            {lead.status && <span className={getStatusClass()}>{lead.status}</span>}
          </div>
        </div>
      )}
    </Draggable>
  );
};

export default LeadCard;
