import { useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface UsePipelineKeyboardProps {
  onAddLead?: () => void;
  onRefresh?: () => void;
}

export const usePipelineKeyboard = ({ onAddLead, onRefresh }: UsePipelineKeyboardProps) => {
  const { toast } = useToast();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check if user is typing in an input field
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
        return;
      }

      // Handle keyboard shortcuts
      if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
          case 'n':
            event.preventDefault();
            if (onAddLead) {
              onAddLead();
            } else {
              toast({
                title: "Keyboard Shortcut",
                description: "Ctrl/Cmd + N: Add new lead (coming soon)",
              });
            }
            break;
          case 'r':
            event.preventDefault();
            if (onRefresh) {
              onRefresh();
            } else {
              toast({
                title: "Pipeline Refreshed",
                description: "Data refreshed successfully",
              });
            }
            break;
        }
      }

      // Handle other shortcuts
      switch (event.key) {
        case '?':
          event.preventDefault();
          toast({
            title: "Keyboard Shortcuts",
            description: "Ctrl/Cmd + N: Add lead, Ctrl/Cmd + R: Refresh, ?: Show help",
          });
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onAddLead, onRefresh, toast]);
};
