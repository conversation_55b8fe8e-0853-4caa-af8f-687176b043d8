import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { oauthService, type OAuthStatus } from '@/services/oauthService';
import { 
  Mail, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw, 
  Unlink,
  ExternalLink,
  Shield,
  Clock
} from 'lucide-react';

interface GmailConnectionProps {
  onConnectionChange?: (connected: boolean) => void;
}

const GmailConnection: React.FC<GmailConnectionProps> = ({ onConnectionChange }) => {
  const { user } = useAuth();
  const location = useLocation();
  const [status, setStatus] = useState<OAuthStatus>({ connected: false });
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState(false);
  const [disconnecting, setDisconnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check OAuth status on component mount
  useEffect(() => {
    if (user?.id) {
      checkOAuthStatus();
    }
  }, [user?.id]);

  // Check for connection changes when component mounts or user changes
  useEffect(() => {
    if (user?.id) {
      // Small delay to allow for OAuth callback processing
      const timer = setTimeout(() => {
        checkOAuthStatus();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [user?.id]);

  const checkOAuthStatus = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);
      const oauthStatus = await oauthService.getGoogleOAuthStatus(user.id);
      setStatus(oauthStatus);
      onConnectionChange?.(oauthStatus.connected);
    } catch (error) {
      console.error('Error checking OAuth status:', error);
      setError('Failed to check Gmail connection status');
    } finally {
      setLoading(false);
    }
  };



  const handleConnectGmail = async () => {
    if (!user?.id) {
      setError('Please log in first to connect Gmail');
      return;
    }

    try {
      setConnecting(true);
      setError(null);

      // Start OAuth flow - this will redirect to Google
      await oauthService.startGoogleOAuth();
    } catch (error) {
      console.error('Error connecting Gmail:', error);
      setError(error instanceof Error ? error.message : 'Failed to connect Gmail');
      setConnecting(false);
    }
  };

  const handleDisconnectGmail = async () => {
    if (!user?.id) return;

    try {
      setDisconnecting(true);
      setError(null);
      await oauthService.disconnectGoogle(user.id);
      setStatus({ connected: false });
      onConnectionChange?.(false);
    } catch (error) {
      console.error('Error disconnecting Gmail:', error);
      setError(error instanceof Error ? error.message : 'Failed to disconnect Gmail');
    } finally {
      setDisconnecting(false);
    }
  };

  const handleRefreshTokens = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);
      await oauthService.refreshGoogleTokens(user.id);
      await checkOAuthStatus();
    } catch (error) {
      console.error('Error refreshing tokens:', error);
      setError(error instanceof Error ? error.message : 'Failed to refresh connection');
    } finally {
      setLoading(false);
    }
  };

  // Auto-connect Gmail if user was redirected from login
  useEffect(() => {
    if (user?.id && location.state?.connectGmail && !status.connected && !connecting && !loading) {
      console.log('Auto-connecting Gmail after login...');
      handleConnectGmail();
    }
  }, [user?.id, location.state?.connectGmail, status.connected, connecting, loading]);

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Checking Gmail connection...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Gmail Integration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div className="flex items-center gap-3">
            <div className={`w-3 h-3 rounded-full ${
              status.connected ? 'bg-green-500' : 'bg-gray-300'
            }`} />
            <div>
              <p className="font-medium">
                {status.connected ? 'Gmail Connected' : 'Gmail Not Connected'}
              </p>
              {status.connected && status.email && (
                <p className="text-sm text-muted-foreground">{status.email}</p>
              )}
              {status.expired && (
                <p className="text-sm text-orange-600">Connection expired - please refresh</p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {status.connected ? (
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Connected
              </Badge>
            ) : (
              <Badge variant="outline">
                <AlertCircle className="h-3 w-3 mr-1" />
                Disconnected
              </Badge>
            )}
          </div>
        </div>

        {status.connected && status.expiresAt && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>
              Expires: {new Date(status.expiresAt).toLocaleDateString()}
            </span>
          </div>
        )}

        <div className="flex gap-2">
          {!status.connected ? (
            <Button 
              onClick={handleConnectGmail}
              disabled={connecting}
              className="flex-1"
            >
              {connecting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Connect Gmail
                </>
              )}
            </Button>
          ) : (
            <>
              {status.expired && (
                <Button 
                  onClick={handleRefreshTokens}
                  disabled={loading}
                  variant="outline"
                  className="flex-1"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Connection
                </Button>
              )}
              
              <Button 
                onClick={handleDisconnectGmail}
                disabled={disconnecting}
                variant="outline"
                className="flex-1"
              >
                {disconnecting ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Disconnecting...
                  </>
                ) : (
                  <>
                    <Unlink className="h-4 w-4 mr-2" />
                    Disconnect
                  </>
                )}
              </Button>
            </>
          )}
        </div>

        <div className="text-xs text-muted-foreground space-y-1">
          <div className="flex items-center gap-1">
            <Shield className="h-3 w-3" />
            <span>Secure OAuth 2.0 connection</span>
          </div>
          <p>
            ZiadaCRM will access your Gmail to read, send, and manage emails.
            You can disconnect at any time.
          </p>
          {connecting && (
            <p className="text-blue-600 dark:text-blue-400">
              You will be redirected to Google to authorize access. After authorization,
              you'll be brought back to ZiadaCRM automatically.
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default GmailConnection;
