{"version": 3, "file": "PostgrestTransformBuilder.d.ts", "sourceRoot": "", "sources": ["../../src/PostgrestTransformBuilder.ts"], "names": [], "mappings": "AAAA,OAAO,gBAAgB,MAAM,oBAAoB,CAAA;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,8BAA8B,CAAA;AACxD,OAAO,EAAE,aAAa,EAAE,uBAAuB,EAAE,MAAM,SAAS,CAAA;AAEhE,MAAM,CAAC,OAAO,OAAO,yBAAyB,CAC5C,MAAM,SAAS,aAAa,EAC5B,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACnC,MAAM,EACN,YAAY,GAAG,OAAO,EACtB,aAAa,GAAG,OAAO,CACvB,SAAQ,gBAAgB,CAAC,MAAM,CAAC;IAChC;;;;;;;;OAQG;IACH,MAAM,CACJ,KAAK,SAAS,MAAM,GAAG,GAAG,EAC1B,YAAY,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,aAAa,EAAE,KAAK,CAAC,EAEzE,OAAO,CAAC,EAAE,KAAK,GACd,yBAAyB,CAAC,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE,YAAY,EAAE,aAAa,CAAC;IA6BtF,KAAK,CAAC,UAAU,SAAS,MAAM,GAAG,MAAM,GAAG,EACzC,MAAM,EAAE,UAAU,EAClB,OAAO,CAAC,EAAE;QAAE,SAAS,CAAC,EAAE,OAAO,CAAC;QAAC,UAAU,CAAC,EAAE,OAAO,CAAC;QAAC,eAAe,CAAC,EAAE,SAAS,CAAA;KAAE,GACnF,IAAI;IACP,KAAK,CACH,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QAAE,SAAS,CAAC,EAAE,OAAO,CAAC;QAAC,UAAU,CAAC,EAAE,OAAO,CAAC;QAAC,eAAe,CAAC,EAAE,MAAM,CAAA;KAAE,GAChF,IAAI;IACP;;OAEG;IACH,KAAK,CAAC,UAAU,SAAS,MAAM,GAAG,MAAM,GAAG,EACzC,MAAM,EAAE,UAAU,EAClB,OAAO,CAAC,EAAE;QAAE,SAAS,CAAC,EAAE,OAAO,CAAC;QAAC,UAAU,CAAC,EAAE,OAAO,CAAC;QAAC,YAAY,CAAC,EAAE,SAAS,CAAA;KAAE,GAChF,IAAI;IACP;;OAEG;IACH,KAAK,CACH,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QAAE,SAAS,CAAC,EAAE,OAAO,CAAC;QAAC,UAAU,CAAC,EAAE,OAAO,CAAC;QAAC,YAAY,CAAC,EAAE,MAAM,CAAA;KAAE,GAC7E,IAAI;IA6CP;;;;;;;;;OASG;IACH,KAAK,CACH,KAAK,EAAE,MAAM,EACb,EACE,YAAY,EACZ,eAA8B,GAC/B,GAAE;QAAE,YAAY,CAAC,EAAE,MAAM,CAAC;QAAC,eAAe,CAAC,EAAE,MAAM,CAAA;KAAO,GAC1D,IAAI;IAMP;;;;;;;;;;;;;;OAcG;IACH,KAAK,CACH,IAAI,EAAE,MAAM,EACZ,EAAE,EAAE,MAAM,EACV,EACE,YAAY,EACZ,eAA8B,GAC/B,GAAE;QAAE,YAAY,CAAC,EAAE,MAAM,CAAC;QAAC,eAAe,CAAC,EAAE,MAAM,CAAA;KAAO,GAC1D,IAAI;IAUP;;;;OAIG;IACH,WAAW,CAAC,MAAM,EAAE,WAAW,GAAG,IAAI;IAKtC;;;;;OAKG;IACH,MAAM,CACJ,SAAS,GAAG,MAAM,SAAS,CAAC,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,GAAG,KAAK,KAC/D,gBAAgB,CAAC,SAAS,CAAC;IAKhC;;;;;OAKG;IACH,WAAW,CACT,SAAS,GAAG,MAAM,SAAS,CAAC,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,GAAG,KAAK,KAC/D,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC;IAYvC;;OAEG;IACH,GAAG,IAAI,gBAAgB,CAAC,MAAM,CAAC;IAK/B;;OAEG;IACH,OAAO,IAAI,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAKpD;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,OAAO,CAAC,EACN,OAAe,EACf,OAAe,EACf,QAAgB,EAChB,OAAe,EACf,GAAW,EACX,MAAe,GAChB,GAAE;QACD,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,QAAQ,CAAC,EAAE,OAAO,CAAA;QAClB,OAAO,CAAC,EAAE,OAAO,CAAA;QACjB,GAAG,CAAC,EAAE,OAAO,CAAA;QACb,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;KACpB,GAAG,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC;IAmB/E;;;;OAIG;IACH,QAAQ,IAAI,IAAI;IAShB;;;;;OAKG;IACH,OAAO,CAAC,SAAS,KAAK,yBAAyB,CAC7C,MAAM,EACN,GAAG,EACH,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,EAC1C,YAAY,EACZ,aAAa,CACd;CASF"}