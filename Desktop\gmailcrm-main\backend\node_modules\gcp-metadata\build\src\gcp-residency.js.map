{"version": 3, "file": "gcp-residency.js", "sourceRoot": "", "sources": ["../../src/gcp-residency.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,2BAA0C;AAC1C,2BAA+C;AAE/C;;GAEG;AACU,QAAA,oBAAoB,GAAG;IAClC,SAAS,EAAE,6BAA6B;IACxC,WAAW,EAAE,+BAA+B;CAC7C,CAAC;AAEF,MAAM,qBAAqB,GAAG,QAAQ,CAAC;AAEvC;;;;;;;;GAQG;AACH,SAAgB,uBAAuB;IACrC;;;;;;;;;;OAUG;IACH,MAAM,eAAe,GACnB,OAAO,CAAC,GAAG,CAAC,aAAa;QACzB,OAAO,CAAC,GAAG,CAAC,aAAa;QACzB,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC;IAExB,OAAO,CAAC,CAAC,eAAe,CAAC;AAC3B,CAAC;AAlBD,0DAkBC;AAED;;;;GAIG;AACH,SAAgB,0BAA0B;IACxC,IAAI,IAAA,aAAQ,GAAE,KAAK,OAAO;QAAE,OAAO,KAAK,CAAC;IAEzC,IAAI;QACF,yBAAyB;QACzB,IAAA,aAAQ,EAAC,4BAAoB,CAAC,SAAS,CAAC,CAAC;QAEzC,qCAAqC;QACrC,MAAM,UAAU,GAAG,IAAA,iBAAY,EAAC,4BAAoB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAE1E,OAAO,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAClC;IAAC,WAAM;QACN,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAdD,gEAcC;AAED;;;;;GAKG;AACH,SAAgB,+BAA+B;IAC7C,MAAM,UAAU,GAAG,IAAA,sBAAiB,GAAE,CAAC;IAEvC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5C,IAAI,CAAC,IAAI;YAAE,SAAS;QAEpB,KAAK,MAAM,EAAC,GAAG,EAAC,IAAI,IAAI,EAAE;YACxB,IAAI,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACnC,OAAO,IAAI,CAAC;aACb;SACF;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAdD,0EAcC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB;IACnC,OAAO,0BAA0B,EAAE,IAAI,+BAA+B,EAAE,CAAC;AAC3E,CAAC;AAFD,sDAEC;AAED;;;;GAIG;AACH,SAAgB,kBAAkB;IAChC,OAAO,uBAAuB,EAAE,IAAI,qBAAqB,EAAE,CAAC;AAC9D,CAAC;AAFD,gDAEC"}