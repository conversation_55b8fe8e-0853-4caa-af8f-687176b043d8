
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Soft pastel background */
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    /* Card with subtle shadow */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Vibrant but not harsh primary color */
    --primary: 238.7 83.5% 66.7%;
    --primary-foreground: 210 40% 98%;

    /* Complementary secondary color */
    --secondary: 271 95.5% 64.9%;
    --secondary-foreground: 210 40% 98%;

    /* Subtle muted colors */
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    /* Gentle destructive color */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Success color */
    --success: 142 76% 45%;
    --success-foreground: 210 40% 98%;

    /* Subtle borders */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    /* Rounded corners */
    --radius: 0.5rem;

    /* Sidebar styling */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Soft dark background - not pure black */
    --background: 222 18% 12%;
    --foreground: 210 40% 98%;

    /* Slightly lighter card background */
    --card: 222 16% 16%;
    --card-foreground: 210 40% 98%;

    --popover: 222 16% 16%;
    --popover-foreground: 210 40% 98%;

    /* Vibrant primary in dark mode */
    --primary: 238.7 83.5% 66.7%;
    --primary-foreground: 210 40% 98%;

    /* Complementary secondary */
    --secondary: 271 95.5% 64.9%;
    --secondary-foreground: 210 40% 98%;

    /* Subtle muted colors */
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    /* Gentle destructive color */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    /* Success color */
    --success: 142 76% 45%;
    --success-foreground: 210 40% 98%;

    /* Subtle borders */
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Sidebar styling */
    --sidebar-background: 222 20% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 238.7 83.5% 66.7%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
}

.pipeline-card {
  @apply border rounded-md p-4 shadow-sm bg-white transition-all hover:shadow-md;
  @apply border-border/50 hover:border-primary/30;
  @apply relative overflow-hidden;
}

.pipeline-card::before {
  content: '';
  @apply absolute top-0 left-0 w-1 h-full bg-primary/20 opacity-0 transition-opacity;
}

.pipeline-card:hover::before {
  @apply opacity-100;
}

.tag {
  @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  @apply transition-colors;
}

.tag-new {
  @apply bg-blue-100 text-blue-800;
}

.tag-contacted {
  @apply bg-yellow-100 text-yellow-800;
}

.tag-qualified {
  @apply bg-purple-100 text-purple-800;
}

.tag-proposal {
  @apply bg-indigo-100 text-indigo-800;
}

.tag-negotiation {
  @apply bg-pink-100 text-pink-800;
}

.tag-won {
  @apply bg-green-100 text-green-800;
}

.tag-lost {
  @apply bg-gray-100 text-gray-800;
}

.drag-handle {
  @apply cursor-move;
}

/* Drag and Drop Enhancements */
.pipeline-stage-content {
  @apply transition-colors duration-200;
  min-height: 100px;
}

.pipeline-stage-content.dragging-over {
  @apply bg-primary/5 border-primary/20;
}

/* Dragging state styles */
.pipeline-card.dragging {
  @apply shadow-lg rotate-2 bg-white border-primary/40 z-50;
  transform: rotate(2deg) scale(1.02);
}

/* Drop zone indicator */
.drop-zone-indicator {
  @apply border-2 border-dashed border-primary/30 bg-primary/5 rounded-md;
  height: 2px;
  margin: 4px 0;
  transition: all 0.2s ease;
}

/* Smooth transitions for cards */
.pipeline-card {
  @apply transition-all duration-200 ease-in-out;
}

.pipeline-card:hover {
  @apply transform scale-105;
}

/* Drag handle visibility */
.pipeline-card .drag-handle {
  @apply opacity-0 transition-opacity duration-200;
}

.pipeline-card:hover .drag-handle {
  @apply opacity-100;
}

/* Mobile Responsiveness Improvements */
@media (max-width: 640px) {
  /* Gmail Integration Mobile Styles */
  .gmail-integration-header {
    @apply flex-col space-y-3;
  }

  .gmail-integration-title {
    @apply text-lg;
  }

  .gmail-integration-description {
    @apply text-sm;
  }

  /* Button improvements for mobile */
  .mobile-button-stack {
    @apply flex-col space-y-2;
  }

  .mobile-button-stack .btn {
    @apply w-full;
  }

  /* Card padding adjustments */
  .mobile-card {
    @apply p-4;
  }

  /* Text wrapping for long URLs */
  .permission-text {
    @apply break-all text-xs;
  }

  /* Improved touch targets */
  .mobile-touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Alert improvements for mobile */
  .alert-mobile {
    @apply p-3;
  }

  .alert-mobile .alert-icon {
    @apply h-4 w-4 mt-0.5 flex-shrink-0;
  }

  .alert-mobile .alert-content {
    @apply min-w-0 flex-1;
  }

  /* Card content spacing */
  .card-mobile {
    @apply p-4 space-y-3;
  }

  /* Header improvements */
  .header-mobile {
    @apply px-4 h-14;
  }

  .header-mobile .title {
    @apply text-lg;
  }

  /* Navigation improvements */
  .nav-mobile {
    @apply space-y-1;
  }

  .nav-mobile .nav-item {
    @apply py-3 px-3 rounded-md;
  }
}

/* Enhanced Button Styles */
.enhanced-back-button {
  @apply relative overflow-hidden;
}

.enhanced-back-button::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-slate-200 to-slate-100 opacity-0 transition-opacity duration-300;
}

.enhanced-back-button:hover::before {
  @apply opacity-100;
}

.enhanced-skip-button {
  @apply relative overflow-hidden;
}

.enhanced-skip-button::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-orange-100 to-orange-50 opacity-0 transition-opacity duration-300;
}

.enhanced-skip-button:hover::before {
  @apply opacity-100;
}

/* Button pulse animation */
@keyframes button-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.button-pulse {
  animation: button-pulse 2s ease-in-out infinite;
}

/* Subtle glow effect */
.button-glow {
  @apply relative;
}

.button-glow::after {
  content: '';
  @apply absolute inset-0 rounded-md opacity-0 transition-opacity duration-300;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.button-glow:hover::after {
  @apply opacity-100;
}

/* Skip button specific glow */
.skip-button-glow::after {
  box-shadow: 0 0 20px rgba(249, 115, 22, 0.3);
}

/* Enhanced Search Styles */
.search-input-enhanced {
  @apply relative overflow-hidden;
}

.search-input-enhanced::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 transition-opacity duration-300;
}

.search-input-enhanced:focus-within::before {
  @apply opacity-100;
}

/* Search button enhancements */
.search-button-prominent {
  @apply relative overflow-hidden;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.2s ease;
}

.search-button-prominent:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0.1) 100%);
  border-color: rgba(59, 130, 246, 0.3);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Search dialog enhancements */
.search-dialog-content {
  @apply max-w-2xl;
}

/* Keyboard shortcut styling */
.kbd-enhanced {
  @apply inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Search result item hover */
.search-result-item {
  @apply transition-all duration-200;
}

.search-result-item:hover {
  @apply bg-accent/50 transform scale-[1.02];
}

/* Mobile Menu Enhancements */
.mobile-logout-button {
  @apply relative overflow-hidden;
  background: linear-gradient(135deg, rgba(254, 226, 226, 1) 0%, rgba(254, 242, 242, 1) 100%);
  border: 1px solid rgba(239, 68, 68, 0.2);
  transition: all 0.2s ease;
}

.mobile-logout-button:hover {
  background: linear-gradient(135deg, rgba(254, 202, 202, 1) 0%, rgba(254, 226, 226, 1) 100%);
  border-color: rgba(239, 68, 68, 0.3);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
}

/* Mobile navigation item enhancements */
.mobile-nav-item {
  @apply transition-all duration-200;
}

.mobile-nav-item:hover {
  @apply transform scale-105 shadow-sm;
}

/* Mobile compose button enhancement */
.mobile-compose-button {
  @apply transition-all duration-200;
}

.mobile-compose-button:hover {
  @apply transform scale-105 shadow-lg;
}
