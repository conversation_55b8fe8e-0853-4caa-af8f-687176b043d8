
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Soft pastel background */
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    /* Card with subtle shadow */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Vibrant but not harsh primary color */
    --primary: 238.7 83.5% 66.7%;
    --primary-foreground: 210 40% 98%;

    /* Complementary secondary color */
    --secondary: 271 95.5% 64.9%;
    --secondary-foreground: 210 40% 98%;

    /* Subtle muted colors */
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    /* Gentle destructive color */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Success color */
    --success: 142 76% 45%;
    --success-foreground: 210 40% 98%;

    /* Subtle borders */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    /* Rounded corners */
    --radius: 0.5rem;

    /* Sidebar styling */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Soft dark background - not pure black */
    --background: 222 18% 12%;
    --foreground: 210 40% 98%;

    /* Slightly lighter card background */
    --card: 222 16% 16%;
    --card-foreground: 210 40% 98%;

    --popover: 222 16% 16%;
    --popover-foreground: 210 40% 98%;

    /* Vibrant primary in dark mode */
    --primary: 238.7 83.5% 66.7%;
    --primary-foreground: 210 40% 98%;

    /* Complementary secondary */
    --secondary: 271 95.5% 64.9%;
    --secondary-foreground: 210 40% 98%;

    /* Subtle muted colors */
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    /* Gentle destructive color */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    /* Success color */
    --success: 142 76% 45%;
    --success-foreground: 210 40% 98%;

    /* Subtle borders */
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Sidebar styling */
    --sidebar-background: 222 20% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 238.7 83.5% 66.7%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
}

.pipeline-card {
  @apply border rounded-md p-4 shadow-sm bg-white transition-all hover:shadow-md;
  @apply border-border/50 hover:border-primary/30;
  @apply relative overflow-hidden;
}

.pipeline-card::before {
  content: '';
  @apply absolute top-0 left-0 w-1 h-full bg-primary/20 opacity-0 transition-opacity;
}

.pipeline-card:hover::before {
  @apply opacity-100;
}

.tag {
  @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  @apply transition-colors;
}

.tag-new {
  @apply bg-blue-100 text-blue-800;
}

.tag-contacted {
  @apply bg-yellow-100 text-yellow-800;
}

.tag-qualified {
  @apply bg-purple-100 text-purple-800;
}

.tag-proposal {
  @apply bg-indigo-100 text-indigo-800;
}

.tag-negotiation {
  @apply bg-pink-100 text-pink-800;
}

.tag-won {
  @apply bg-green-100 text-green-800;
}

.tag-lost {
  @apply bg-gray-100 text-gray-800;
}

.drag-handle {
  @apply cursor-move;
}

/* Drag and Drop Enhancements */
.pipeline-stage-content {
  @apply transition-colors duration-200;
  min-height: 100px;
}

.pipeline-stage-content.dragging-over {
  @apply bg-primary/5 border-primary/20;
}

/* Dragging state styles */
.pipeline-card.dragging {
  @apply shadow-lg rotate-2 bg-white border-primary/40 z-50;
  transform: rotate(2deg) scale(1.02);
}

/* Drop zone indicator */
.drop-zone-indicator {
  @apply border-2 border-dashed border-primary/30 bg-primary/5 rounded-md;
  height: 2px;
  margin: 4px 0;
  transition: all 0.2s ease;
}

/* Smooth transitions for cards */
.pipeline-card {
  @apply transition-all duration-200 ease-in-out;
}

.pipeline-card:hover {
  @apply transform scale-105;
}

/* Drag handle visibility */
.pipeline-card .drag-handle {
  @apply opacity-0 transition-opacity duration-200;
}

.pipeline-card:hover .drag-handle {
  @apply opacity-100;
}
