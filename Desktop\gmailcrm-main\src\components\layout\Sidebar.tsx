
import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Inbox,
  Users,
  Calendar,
  PieChart,
  MessageCircle,
  Settings,
  Mail,
  LogOut,
  BarChart3,
  FileText,
  Zap,
  ChevronLeft,
  ChevronRight,
  Home,
  Phone,
  Target,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import LogoutButton from '@/components/auth/LogoutButton';
import { cn } from '@/lib/utils';

const Sidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const location = useLocation();

  const navItems = [
    {
      name: 'Dashboard',
      icon: <Home className="h-5 w-5" />,
      href: '/dashboard',
      badge: null
    },
    {
      name: 'Gmail Inbox',
      icon: <Inbox className="h-5 w-5" />,
      href: '/inbox',
      badge: '12'
    },
    {
      name: 'Pipeline',
      icon: <Target className="h-5 w-5" />,
      href: '/pipeline',
      badge: null
    },
    {
      name: 'Contacts',
      icon: <Users className="h-5 w-5" />,
      href: '/contacts',
      badge: null
    },
    {
      name: 'Conversations',
      icon: <MessageCircle className="h-5 w-5" />,
      href: '/conversations',
      badge: '3'
    },
    {
      name: 'Calendar',
      icon: <Calendar className="h-5 w-5" />,
      href: '/calendar',
      badge: null
    },
    {
      name: 'Analytics',
      icon: <TrendingUp className="h-5 w-5" />,
      href: '/analytics',
      badge: null
    },
    {
      name: 'Templates',
      icon: <FileText className="h-5 w-5" />,
      href: '/templates',
      badge: null
    },
  ];

  const bottomNavItems = [
    {
      name: 'Settings',
      icon: <Settings className="h-5 w-5" />,
      href: '/settings',
      badge: null
    },
  ];

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return location.pathname === '/dashboard' || location.pathname === '/';
    }
    return location.pathname === href;
  };

  return (
    <aside className={cn(
      "hidden md:flex flex-col border-r bg-sidebar transition-all duration-300 ease-in-out",
      isCollapsed ? "w-16" : "w-72"
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-sidebar-border">
        {!isCollapsed && (
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">Z</span>
            </div>
            <div>
              <h2 className="text-lg font-bold text-sidebar-foreground">ZiadaCRM</h2>
              <p className="text-xs text-sidebar-foreground/60">Gmail CRM Suite</p>
            </div>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8 text-sidebar-foreground/60 hover:text-sidebar-foreground hover:bg-sidebar-accent"
        >
          {isCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Compose Button */}
      <div className="p-4">
        <Button
          className={cn(
            "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200",
            isCollapsed ? "w-8 h-8 p-0" : "w-full"
          )}
        >
          <Mail className={cn("h-4 w-4", !isCollapsed && "mr-2")} />
          {!isCollapsed && "Compose"}
        </Button>
      </div>

      {/* Navigation */}
      <div className="flex-1 px-4 pb-4 flex flex-col min-h-0">
        <nav className="space-y-1 flex-1 overflow-y-auto sidebar-scroll">
          {navItems.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 group relative",
                isActive(item.href)
                  ? "bg-sidebar-accent text-sidebar-accent-foreground shadow-sm"
                  : "text-sidebar-foreground/70 hover:bg-sidebar-accent/50 hover:text-sidebar-foreground"
              )}
            >
              <div className={cn(
                "flex items-center justify-center",
                isActive(item.href) ? "text-blue-600" : ""
              )}>
                {item.icon}
              </div>

              {!isCollapsed && (
                <>
                  <span className="flex-1">{item.name}</span>
                  {item.badge && (
                    <Badge
                      variant="secondary"
                      className="h-5 px-1.5 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
                    >
                      {item.badge}
                    </Badge>
                  )}
                </>
              )}

              {/* Active indicator */}
              {isActive(item.href) && (
                <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-6 bg-blue-600 rounded-r-full" />
              )}

              {/* Tooltip for collapsed state */}
              {isCollapsed && (
                <div className="absolute left-full ml-2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  {item.name}
                  {item.badge && (
                    <span className="ml-1 px-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded text-xs">
                      {item.badge}
                    </span>
                  )}
                </div>
              )}
            </Link>
          ))}
        </nav>

        {/* Bottom Navigation Items - Properly positioned */}
        <div className="space-y-1 mt-auto pt-4 border-t border-sidebar-border flex-shrink-0">
          {/* Theme Toggle */}
          <div className={cn(
            "flex items-center gap-3 rounded-lg px-3 py-2.5",
            isCollapsed ? "justify-center" : ""
          )}>
            <ThemeToggle />
            {!isCollapsed && (
              <span className="text-sm font-medium text-sidebar-foreground/70">
                Theme
              </span>
            )}
          </div>

          {/* Settings */}
          {bottomNavItems.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 group relative",
                isActive(item.href)
                  ? "bg-sidebar-accent text-sidebar-accent-foreground"
                  : "text-sidebar-foreground/70 hover:bg-sidebar-accent/50 hover:text-sidebar-foreground"
              )}
            >
              <div className={cn(
                "flex items-center justify-center",
                isActive(item.href) ? "text-blue-600" : ""
              )}>
                {item.icon}
              </div>

              {!isCollapsed && <span className="flex-1">{item.name}</span>}

              {/* Tooltip for collapsed state */}
              {isCollapsed && (
                <div className="absolute left-full ml-2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  {item.name}
                </div>
              )}
            </Link>
          ))}

          {/* Logout */}
          <div className={cn(
            "flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-sidebar-foreground/70 hover:bg-sidebar-accent/50 hover:text-sidebar-foreground transition-all duration-200 cursor-pointer group relative"
          )}>
            <LogOut className="h-5 w-5" />
            {!isCollapsed && (
              <LogoutButton
                variant="ghost"
                className="flex-1 justify-start p-0 h-auto font-medium text-sidebar-foreground/70 hover:text-sidebar-foreground hover:bg-transparent"
              />
            )}

            {/* Tooltip for collapsed state */}
            {isCollapsed && (
              <div className="absolute left-full ml-2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                Logout
              </div>
            )}
          </div>
        </div>
      </div>


    </aside>
  );
};

export default Sidebar;
