
import React from 'react';
import { MoreHorizon<PERSON> } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import LeadCard from './LeadCard';
import { Droppable } from '@hello-pangea/dnd';

export interface LeadItem {
  id: string;
  name: string;
  company: string;
  email: string;
  value: string;
  lastContact: string;
  status?: 'won' | 'lost';
}

interface StageProps {
  stage: {
    id: string;
    title: string;
    items: LeadItem[];
  };
}

const PipelineStage = ({ stage }: StageProps) => {
  return (
    <div className="pipeline-stage">
      <div className="pipeline-stage-header">
        <h3 className="font-medium text-sm">{stage.title} <span className="text-muted-foreground ml-1">({stage.items.length})</span></h3>
        <button className="text-muted-foreground hover:text-foreground p-1 rounded-sm">
          <MoreHorizontal className="h-4 w-4" />
        </button>
      </div>

      <Droppable droppableId={stage.id}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={`pipeline-stage-content space-y-3 transition-all duration-200 ${
              snapshot.isDraggingOver
                ? 'bg-primary/5 border-2 border-dashed border-primary/30 rounded-md'
                : 'border-2 border-transparent'
            }`}
            style={{
              minHeight: '100px',
              padding: '8px'
            }}
          >
            {stage.items.map((lead, index) => (
              <LeadCard key={lead.id} lead={lead} index={index} />
            ))}

            {stage.items.length === 0 && (
              <div className="flex items-center justify-center h-24 border border-dashed rounded-md text-sm text-muted-foreground">
                No leads yet
              </div>
            )}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  );
};

export default PipelineStage;
