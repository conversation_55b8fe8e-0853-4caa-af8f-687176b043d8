
import React, { useState } from 'react';
import { Search, Menu, Command } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import UserProfile from '@/components/auth/UserProfile';
import { useAuth } from '@/contexts/AuthContext';
import NotificationsDropdown from '@/components/notifications/NotificationsDropdown';
import SettingsDropdown from '@/components/settings/SettingsDropdown';
import MobileMenu from './MobileMenu';
import SearchDialog from './SearchDialog';

const Header = () => {
  const { isAuthenticated } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);

  return (
    <header className="border-b bg-background">
      <div className="h-16 flex items-center justify-between px-4 sm:px-6">
        <div className="flex items-center gap-2 sm:gap-4">
          {/* Mobile Menu Button */}
          {isAuthenticated && (
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setMobileMenuOpen(true)}
            >
              <Menu className="h-5 w-5" />
            </Button>
          )}

          <h1 className="text-lg sm:text-xl font-bold text-primary">ZiadaCRM</h1>

          {/* Desktop Search */}
          <div className="hidden lg:block relative w-80">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search leads, contacts, emails..."
              className="pl-10 pr-4 py-2 bg-gradient-to-r from-muted/50 to-background border border-border hover:border-border/80 focus-visible:ring-2 focus-visible:ring-primary focus-visible:border-primary shadow-sm transition-all duration-200"
              onClick={() => setSearchOpen(true)}
              readOnly
            />
            <div className="absolute right-2 top-2 flex items-center gap-1">
              <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                <span className="text-xs">⌘</span>K
              </kbd>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2 sm:gap-4">
          {isAuthenticated && (
            <>
              {/* Mobile/Tablet Search Button */}
              <Button
                variant="outline"
                size="icon"
                className="lg:hidden bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20 hover:from-primary/20 hover:to-primary/10 hover:border-primary/30 shadow-sm transition-all duration-200 hover:scale-105"
                onClick={() => setSearchOpen(true)}
              >
                <Search className="h-4 w-4 text-primary" />
              </Button>

              <NotificationsDropdown />
              <SettingsDropdown />
              <UserProfile />
            </>
          )}
        </div>
      </div>

      {/* Mobile Menu */}
      {isAuthenticated && (
        <MobileMenu
          open={mobileMenuOpen}
          onOpenChange={setMobileMenuOpen}
        />
      )}

      {/* Search Dialog */}
      {isAuthenticated && (
        <SearchDialog
          open={searchOpen}
          onOpenChange={setSearchOpen}
        />
      )}
    </header>
  );
};

export default Header;
