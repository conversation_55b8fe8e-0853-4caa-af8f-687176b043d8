
import React, { useState } from 'react';
import { ChevronDown, Plus, HelpCircle } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import PipelineStage from './PipelineStage';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { LeadItem } from './PipelineStage'; // Import the LeadItem type from PipelineStage
import { DragDropContext, DropResult } from '@hello-pangea/dnd';
import { usePipelineKeyboard } from '@/hooks/usePipelineKeyboard';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

// Define Stage type to ensure consistent typing
interface PipelineStage {
  id: string;
  title: string;
  items: LeadItem[];
}

// Sample initial data
const initialPipelineData: { stages: PipelineStage[] } = {
  stages: [
    {
      id: 'new',
      title: 'New Leads',
      items: [
        { id: '1', name: '<PERSON>', company: 'Acme Inc.', email: '<EMAIL>', value: '$2,500', lastContact: '2 days ago' },
        { id: '2', name: '<PERSON>', company: 'XYZ Corp', email: '<EMAIL>', value: '$5,000', lastContact: '1 day ago' },
      ]
    },
    {
      id: 'contacted',
      title: 'Contacted',
      items: [
        { id: '3', name: 'James Brown', company: 'ABC Ltd', email: '<EMAIL>', value: '$3,200', lastContact: '3 days ago' },
      ]
    },
    {
      id: 'qualified',
      title: 'Qualified',
      items: [
        { id: '4', name: 'Emily Davis', company: 'Tech Solutions', email: '<EMAIL>', value: '$7,500', lastContact: '5 hours ago' },
        { id: '5', name: 'Michael Wilson', company: 'Global Services', email: '<EMAIL>', value: '$4,800', lastContact: 'Yesterday' },
      ]
    },
    {
      id: 'proposal',
      title: 'Proposal',
      items: []
    },
    {
      id: 'negotiation',
      title: 'Negotiation',
      items: [
        { id: '6', name: 'Lisa Rodriguez', company: 'Innovate Inc.', email: '<EMAIL>', value: '$12,000', lastContact: '3 days ago' },
      ]
    },
    {
      id: 'closed',
      title: 'Closed',
      items: [
        { id: '7', name: 'Robert Taylor', company: 'Prime Consulting', email: '<EMAIL>', value: '$9,500', lastContact: '1 week ago', status: 'won' as const },
        { id: '8', name: 'Michelle Lee', company: 'Forward Media', email: '<EMAIL>', value: '$6,000', lastContact: '2 weeks ago', status: 'lost' as const },
      ]
    },
  ]
};

const Pipeline = () => {
  const [pipelineData, setPipelineData] = useState(initialPipelineData);
  const { toast } = useToast();

  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    // If no destination, return early
    if (!destination) {
      return;
    }

    // If dropped in the same position, return early
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    // Find the source and destination stages
    const sourceStageIndex = pipelineData.stages.findIndex(
      stage => stage.id === source.droppableId
    );
    const destStageIndex = pipelineData.stages.findIndex(
      stage => stage.id === destination.droppableId
    );

    if (sourceStageIndex === -1 || destStageIndex === -1) {
      return;
    }

    const newStages = [...pipelineData.stages];
    const sourceStage = { ...newStages[sourceStageIndex] };
    const destStage = { ...newStages[destStageIndex] };

    // Find the lead being moved
    const leadToMove = sourceStage.items.find(item => item.id === draggableId);
    if (!leadToMove) {
      return;
    }

    // Remove lead from source stage
    sourceStage.items = sourceStage.items.filter(item => item.id !== draggableId);

    // Add lead to destination stage at the correct position
    destStage.items.splice(destination.index, 0, leadToMove);

    // Update the stages array
    newStages[sourceStageIndex] = sourceStage;
    newStages[destStageIndex] = destStage;

    // Update state
    setPipelineData({ stages: newStages });

    // Show success toast with more context
    const stageChangeMessage = sourceStageIndex !== destStageIndex
      ? `${leadToMove.name} moved from ${sourceStage.title} to ${destStage.title}`
      : `${leadToMove.name} reordered in ${destStage.title}`;

    toast({
      title: "Pipeline updated",
      description: stageChangeMessage,
    });

    // TODO: Here you would typically make an API call to persist the change
    // Example: await updateLeadStage(leadToMove.id, destination.droppableId, destination.index);
  };

  const handleAddLead = () => {
    toast({
      title: "Feature Coming Soon",
      description: "Adding new leads will be available in the next update.",
    });
  };

  // Calculate dynamic analytics
  const calculateAnalytics = () => {
    const allLeads = pipelineData.stages.flatMap(stage => stage.items);
    const openDeals = allLeads.filter(lead => !lead.status || (lead.status !== 'won' && lead.status !== 'lost'));
    const wonDeals = allLeads.filter(lead => lead.status === 'won');
    const totalDeals = allLeads.length;

    const totalValue = allLeads.reduce((sum, lead) => {
      const value = parseFloat(lead.value.replace(/[$,]/g, ''));
      return sum + (isNaN(value) ? 0 : value);
    }, 0);

    const conversionRate = totalDeals > 0 ? Math.round((wonDeals.length / totalDeals) * 100) : 0;

    return {
      openDeals: openDeals.length,
      totalValue: `$${totalValue.toLocaleString()}`,
      conversionRate: `${conversionRate}%`
    };
  };

  const analytics = calculateAnalytics();

  // Enable keyboard shortcuts
  usePipelineKeyboard({
    onAddLead: handleAddLead,
    onRefresh: () => {
      // In a real app, this would refetch data from the API
      toast({
        title: "Pipeline Refreshed",
        description: "Data refreshed successfully",
      });
    }
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div>
            <h1 className="text-2xl font-bold">Pipeline</h1>
            <p className="text-muted-foreground">Manage your leads and deals</p>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <button className="text-muted-foreground hover:text-foreground p-1 rounded-sm">
                <HelpCircle className="h-4 w-4" />
              </button>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm">
                <p className="font-medium mb-1">Drag & Drop Tips:</p>
                <ul className="text-xs space-y-1">
                  <li>• Drag leads between stages</li>
                  <li>• Use grip handle to drag</li>
                  <li>• Ctrl/Cmd + N: Add lead</li>
                  <li>• Press ? for help</li>
                </ul>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <ChevronDown className="mr-1 h-4 w-4" />
            Filter
          </Button>
          <Button size="sm" onClick={handleAddLead}>
            <Plus className="mr-1 h-4 w-4" />
            Add Lead
          </Button>
        </div>
      </div>

      {/* Analytics Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Open Deals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.openDeals}</div>
            <p className="text-xs text-muted-foreground">Active opportunities</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Deal Value
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalValue}</div>
            <p className="text-xs text-muted-foreground">Total pipeline value</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Conversion Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.conversionRate}</div>
            <p className="text-xs text-muted-foreground">Won deals ratio</p>
          </CardContent>
        </Card>
      </div>

      {/* Pipeline Board */}
      <div className="relative">
        <DragDropContext onDragEnd={handleDragEnd}>
          <div className="pipeline-container">
            {pipelineData.stages.map((stage) => (
              <PipelineStage key={stage.id} stage={stage} />
            ))}
          </div>
        </DragDropContext>
      </div>
    </div>
  );
};

export default Pipeline;
