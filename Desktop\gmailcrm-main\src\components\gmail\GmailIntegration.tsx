
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { AlertCircle, Check, Mail, ArrowLeft, X } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import DashboardLayout from '@/components/layout/DashboardLayout';

interface GmailIntegrationProps {
  isConnected?: boolean;
}

const GmailIntegration = ({ isConnected = false }: GmailIntegrationProps) => {
  const [connected, setConnected] = useState(isConnected);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleConnect = () => {
    setLoading(true);
    // Simulate OAuth connection flow
    setTimeout(() => {
      setLoading(false);
      setConnected(true);
    }, 2000);
  };

  const handleDisconnect = () => {
    setConnected(false);
  };

  const handleGoBack = () => {
    navigate(-1); // Go back to previous page
  };

  const handleSkip = () => {
    navigate('/'); // Go to dashboard
  };

  const requiredScopes = [
    'https://www.googleapis.com/auth/gmail.readonly',
    'https://www.googleapis.com/auth/gmail.send',
    'https://www.googleapis.com/auth/gmail.labels',
    'https://www.googleapis.com/auth/gmail.metadata'
  ];

  return (
    <DashboardLayout>
      {/* Header with navigation */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleGoBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Gmail Integration</h1>
            <p className="text-muted-foreground">Connect your Gmail account to enhance your CRM experience</p>
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleSkip}
          className="flex items-center gap-2"
        >
          <X className="h-4 w-4" />
          Skip for now
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardDescription>
            Connect your Gmail account to enable email tracking, templates, and lead management.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!connected ? (
            <div className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Connection Required</AlertTitle>
                <AlertDescription>
                  We need access to your Gmail account to provide CRM functionality.
                </AlertDescription>
              </Alert>

              <div className="bg-muted/50 p-4 rounded-md">
                <h4 className="font-medium mb-2">Required Permissions:</h4>
                <ul className="space-y-1 text-sm">
                  {requiredScopes.map((scope, index) => (
                    <li key={index} className="flex items-start">
                      <span className="mr-2">•</span>
                      <span>{scope}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <Button
                onClick={handleConnect}
                className="w-full"
                disabled={loading}
              >
                {loading ? 'Connecting...' : 'Connect Gmail'}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <Alert variant="default" className="bg-success/20 text-success-foreground border-success/30">
                <Check className="h-4 w-4" />
                <AlertTitle>Connected</AlertTitle>
                <AlertDescription>
                  Your Gmail account is successfully connected to InboxFlow.
                </AlertDescription>
              </Alert>

              <div className="flex items-center justify-between bg-muted/50 p-3 rounded-md">
                <div>
                  <p className="font-medium text-sm"><EMAIL></p>
                  <p className="text-xs text-muted-foreground">All permissions granted</p>
                </div>
                <Button variant="outline" size="sm">Disconnect</Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </DashboardLayout>
  );
};

export default GmailIntegration;
