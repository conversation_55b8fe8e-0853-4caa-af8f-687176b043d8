
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { AlertCircle, Check, Mail, ArrowLeft, X } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { useToast } from '@/components/ui/use-toast';

interface GmailIntegrationProps {
  isConnected?: boolean;
}

const GmailIntegration = ({ isConnected = false }: GmailIntegrationProps) => {
  const [connected, setConnected] = useState(isConnected);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleConnect = () => {
    setLoading(true);
    // Simulate OAuth connection flow
    setTimeout(() => {
      setLoading(false);
      setConnected(true);
      toast({
        title: "Gmail Connected Successfully!",
        description: "Your Gmail account is now connected to your CRM.",
      });
    }, 2000);
  };

  const handleDisconnect = () => {
    setConnected(false);
    toast({
      title: "Gmail Disconnected",
      description: "Your Gmail account has been disconnected from the CRM.",
    });
  };

  const handleGoBack = () => {
    navigate(-1); // Go back to previous page
  };

  const handleSkip = () => {
    toast({
      title: "Gmail Integration Skipped",
      description: "You can set up Gmail integration later from your settings.",
    });
    navigate('/'); // Go to dashboard
  };

  const requiredScopes = [
    'https://www.googleapis.com/auth/gmail.readonly',
    'https://www.googleapis.com/auth/gmail.send',
    'https://www.googleapis.com/auth/gmail.labels',
    'https://www.googleapis.com/auth/gmail.metadata'
  ];

  return (
    <DashboardLayout>
      {/* Header with navigation - Mobile Responsive */}
      <div className="mb-4 sm:mb-6">
        {/* Mobile Header */}
        <div className="flex items-center justify-between mb-4 sm:hidden">
          <Button
            variant="secondary"
            size="default"
            onClick={handleGoBack}
            className="flex items-center gap-2 bg-slate-100 hover:bg-slate-200 text-slate-700 border border-slate-300 shadow-sm font-medium px-4 py-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <Button
            variant="outline"
            size="default"
            onClick={handleSkip}
            className="flex items-center gap-2 border-2 border-orange-200 text-orange-600 hover:bg-orange-50 hover:border-orange-300 shadow-sm font-medium px-4 py-2"
          >
            <X className="h-4 w-4" />
            Skip
          </Button>
        </div>

        {/* Mobile Title */}
        <div className="sm:hidden mb-4">
          <h1 className="text-xl font-bold">Gmail Integration</h1>
          <p className="text-sm text-muted-foreground mt-1">Connect your Gmail account to enhance your CRM experience</p>
        </div>

        {/* Desktop Header */}
        <div className="hidden sm:flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleGoBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Gmail Integration</h1>
              <p className="text-muted-foreground">Connect your Gmail account to enhance your CRM experience</p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleSkip}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Skip for now
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-4 sm:pb-6">
          <CardDescription className="text-sm sm:text-base">
            Connect your Gmail account to enable email tracking, templates, and lead management.
            You can skip this step and set it up later from your settings.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!connected ? (
            <div className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <div className="min-w-0">
                  <AlertTitle className="text-sm sm:text-base">Connection Required</AlertTitle>
                  <AlertDescription className="text-xs sm:text-sm">
                    We need access to your Gmail account to provide CRM functionality.
                  </AlertDescription>
                </div>
              </Alert>

              <div className="bg-muted/50 p-3 sm:p-4 rounded-md">
                <h4 className="font-medium mb-2 text-sm sm:text-base">Required Permissions:</h4>
                <ul className="space-y-2 text-xs sm:text-sm">
                  {requiredScopes.map((scope, index) => (
                    <li key={index} className="flex items-start">
                      <span className="mr-2 mt-0.5 flex-shrink-0">•</span>
                      <span className="break-all">{scope}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={handleConnect}
                  className="flex-1 w-full sm:w-auto"
                  disabled={loading}
                  size="default"
                >
                  {loading ? 'Connecting...' : 'Connect Gmail'}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleSkip}
                  disabled={loading}
                  className="w-full sm:w-auto sm:px-6"
                  size="default"
                >
                  Skip
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Alert variant="default" className="bg-success/20 text-success-foreground border-success/30">
                <Check className="h-4 w-4 flex-shrink-0" />
                <div className="min-w-0">
                  <AlertTitle className="text-sm sm:text-base">Connected</AlertTitle>
                  <AlertDescription className="text-xs sm:text-sm">
                    Your Gmail account is successfully connected to InboxFlow.
                  </AlertDescription>
                </div>
              </Alert>

              <div className="bg-muted/50 p-3 rounded-md">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <div className="min-w-0 flex-1">
                    <p className="font-medium text-sm truncate"><EMAIL></p>
                    <p className="text-xs text-muted-foreground">All permissions granted</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDisconnect}
                    className="w-full sm:w-auto"
                  >
                    Disconnect
                  </Button>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 pt-2">
                <Button
                  onClick={() => navigate('/')}
                  className="flex-1 w-full sm:w-auto"
                  size="default"
                >
                  Continue to Dashboard
                </Button>
                <Button
                  variant="outline"
                  onClick={handleGoBack}
                  className="w-full sm:w-auto"
                  size="default"
                >
                  Back
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </DashboardLayout>
  );
};

export default GmailIntegration;
