import { useAuth } from '@/contexts/AuthContext';

const API_URL = import.meta.env.VITE_API_URL;

export interface OAuthStatus {
  connected: boolean;
  email?: string;
  expiresAt?: string;
  refreshed?: boolean;
  expired?: boolean;
}

export interface OAuthCallbackData {
  success: boolean;
  message: string;
  user?: {
    email: string;
    name: string;
    picture: string;
  };
  gmail?: {
    emailAddress: string;
    messagesTotal: number;
    threadsTotal: number;
  };
}

class OAuthService {
  /**
   * Get Google OAuth authorization URL
   */
  async getGoogleAuthUrl(): Promise<string> {
    try {
      const response = await fetch(`${API_URL}/oauth/google/url`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to get authorization URL');
      }
      
      return data.authUrl;
    } catch (error) {
      console.error('Error getting auth URL:', error);
      throw error;
    }
  }

  /**
   * Handle OAuth callback with authorization code
   */
  async handleGoogleCallback(code: string, userId: string): Promise<OAuthCallbackData> {
    try {
      console.log('Sending OAuth callback request:', { code: code.substring(0, 20) + '...', userId });

      const response = await fetch(`${API_URL}/oauth/google/callback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code, userId }),
      });

      console.log('OAuth callback response status:', response.status);

      const data = await response.json();
      console.log('OAuth callback response data:', data);

      if (!response.ok) {
        console.error('OAuth callback failed:', {
          status: response.status,
          statusText: response.statusText,
          error: data.error,
          details: data.details
        });
        throw new Error(data.error || `Failed to complete OAuth flow (${response.status})`);
      }

      return data;
    } catch (error) {
      console.error('Error handling OAuth callback:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Cannot connect to server. Please make sure the backend is running.');
      }
      throw error;
    }
  }

  /**
   * Check Google OAuth connection status
   */
  async getGoogleOAuthStatus(userId: string): Promise<OAuthStatus> {
    try {
      const response = await fetch(`${API_URL}/oauth/google/status/${userId}`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to check OAuth status');
      }
      
      return data;
    } catch (error) {
      console.error('Error checking OAuth status:', error);
      throw error;
    }
  }

  /**
   * Disconnect Google OAuth
   */
  async disconnectGoogle(userId: string): Promise<void> {
    try {
      const response = await fetch(`${API_URL}/oauth/google/disconnect/${userId}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to disconnect OAuth');
      }
    } catch (error) {
      console.error('Error disconnecting OAuth:', error);
      throw error;
    }
  }

  /**
   * Refresh Google OAuth tokens
   */
  async refreshGoogleTokens(userId: string): Promise<void> {
    try {
      const response = await fetch(`${API_URL}/oauth/google/refresh/${userId}`, {
        method: 'POST',
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to refresh tokens');
      }
    } catch (error) {
      console.error('Error refreshing tokens:', error);
      throw error;
    }
  }

  /**
   * Start Google OAuth flow
   */
  async startGoogleOAuth(): Promise<void> {
    try {
      const authUrl = await this.getGoogleAuthUrl();

      // Use full page redirect to avoid COOP issues
      // This is more reliable than popup windows
      window.location.href = authUrl;
    } catch (error) {
      console.error('Error starting OAuth flow:', error);
      throw error;
    }
  }

  /**
   * Alternative OAuth flow using new tab (fallback method)
   */
  async startGoogleOAuthNewTab(): Promise<void> {
    try {
      const authUrl = await this.getGoogleAuthUrl();

      // Open in new tab as fallback
      const newTab = window.open(authUrl, '_blank');

      if (!newTab) {
        throw new Error('Failed to open OAuth tab. Please allow popups for this site.');
      }
    } catch (error) {
      console.error('Error starting OAuth flow:', error);
      throw error;
    }
  }
}

export const oauthService = new OAuthService();
