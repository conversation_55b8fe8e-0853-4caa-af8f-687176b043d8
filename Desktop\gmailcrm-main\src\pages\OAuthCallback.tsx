import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Check<PERSON>ircle, AlertCircle, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { oauthService } from '@/services/oauthService';

const OAuthCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing OAuth callback...');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const error = searchParams.get('error');

        if (error) {
          throw new Error(`OAuth error: ${error}`);
        }

        if (!code) {
          throw new Error('No authorization code received');
        }

        if (!user?.id) {
          // User is not authenticated, redirect to login with return URL
          setStatus('error');
          setMessage('Please log in to ZiadaCRM first, then try connecting Gmail again.');

          setTimeout(() => {
            navigate('/login?returnTo=/inbox&connectGmail=true');
          }, 3000);
          return;
        }

        setMessage('Connecting to Gmail...');

        // Send the authorization code to our backend
        const result = await oauthService.handleGoogleCallback(code, user.id);

        if (result.success) {
          setStatus('success');
          setMessage('Gmail connected successfully! Redirecting to inbox...');

          // Redirect to inbox after successful connection
          setTimeout(() => {
            navigate('/inbox');
          }, 2000);
        } else {
          throw new Error(result.message || 'Failed to connect Gmail');
        }
      } catch (error) {
        console.error('OAuth callback error:', error);
        setStatus('error');
        setMessage(error instanceof Error ? error.message : 'OAuth callback failed');

        // Redirect to inbox after error so user can try again
        setTimeout(() => {
          navigate('/inbox');
        }, 5000);
      }
    };

    handleCallback();
  }, [searchParams, navigate, user?.id]);

  const getIcon = () => {
    switch (status) {
      case 'loading':
        return <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />;
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-8 w-8 text-red-600" />;
    }
  };

  const getAlertVariant = () => {
    switch (status) {
      case 'success':
        return 'default';
      case 'error':
        return 'destructive' as const;
      default:
        return 'default';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              {getIcon()}
            </div>
            
            <div>
              <h1 className="text-xl font-semibold text-foreground">
                Gmail OAuth
              </h1>
              <p className="text-sm text-muted-foreground mt-1">
                {status === 'loading' && 'Processing your Gmail connection...'}
                {status === 'success' && 'Connection successful!'}
                {status === 'error' && 'Connection failed'}
              </p>
            </div>

            <Alert variant={getAlertVariant()}>
              <AlertDescription className="text-center">
                {message}
              </AlertDescription>
            </Alert>

            {status === 'error' && (
              <div className="text-xs text-muted-foreground">
                {message.includes('log in to ZiadaCRM first') ? (
                  <>
                    <p>You need to be logged into ZiadaCRM to connect Gmail.</p>
                    <p className="mt-2 text-blue-600 dark:text-blue-400">
                      You'll be redirected to the login page. After logging in, Gmail will be connected automatically.
                    </p>
                  </>
                ) : (
                  <>
                    <p>If you're seeing this error:</p>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>Make sure you granted all required permissions</li>
                      <li>Check that your Google account is accessible</li>
                      <li>Ensure you're logged into ZiadaCRM</li>
                      <li>Try the connection process again from the inbox page</li>
                    </ul>
                    <p className="mt-2 text-blue-600 dark:text-blue-400">
                      You'll be redirected to the inbox page in a few seconds to try again.
                    </p>
                  </>
                )}
              </div>
            )}

            {window.opener && (
              <p className="text-xs text-muted-foreground">
                This window will close automatically.
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OAuthCallback;
