import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Check<PERSON>ircle, AlertCircle, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { oauthService } from '@/services/oauthService';

const OAuthCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing OAuth callback...');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const error = searchParams.get('error');
        const state = searchParams.get('state');

        if (error) {
          throw new Error(`OAuth error: ${error}`);
        }

        if (!code) {
          throw new Error('No authorization code received');
        }

        // Send the code to the parent window
        if (window.opener) {
          window.opener.postMessage({
            type: 'OAUTH_CALLBACK',
            code,
            state
          }, window.location.origin);
          
          setStatus('success');
          setMessage('Gmail connected successfully! You can close this window.');
          
          // Close the popup after a short delay
          setTimeout(() => {
            window.close();
          }, 2000);
        } else {
          // If not in a popup, redirect to dashboard
          setStatus('success');
          setMessage('Gmail connected successfully! Redirecting...');
          
          setTimeout(() => {
            navigate('/dashboard');
          }, 2000);
        }
      } catch (error) {
        console.error('OAuth callback error:', error);
        setStatus('error');
        setMessage(error instanceof Error ? error.message : 'OAuth callback failed');

        // Send error to parent window if in popup
        if (window.opener) {
          window.opener.postMessage({
            type: 'OAUTH_ERROR',
            error: error instanceof Error ? error.message : 'OAuth callback failed'
          }, window.location.origin);
          
          setTimeout(() => {
            window.close();
          }, 3000);
        }
      }
    };

    handleCallback();
  }, [searchParams, navigate]);

  const getIcon = () => {
    switch (status) {
      case 'loading':
        return <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />;
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-8 w-8 text-red-600" />;
    }
  };

  const getAlertVariant = () => {
    switch (status) {
      case 'success':
        return 'default';
      case 'error':
        return 'destructive' as const;
      default:
        return 'default';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              {getIcon()}
            </div>
            
            <div>
              <h1 className="text-xl font-semibold text-foreground">
                Gmail OAuth
              </h1>
              <p className="text-sm text-muted-foreground mt-1">
                {status === 'loading' && 'Processing your Gmail connection...'}
                {status === 'success' && 'Connection successful!'}
                {status === 'error' && 'Connection failed'}
              </p>
            </div>

            <Alert variant={getAlertVariant()}>
              <AlertDescription className="text-center">
                {message}
              </AlertDescription>
            </Alert>

            {status === 'error' && (
              <div className="text-xs text-muted-foreground">
                <p>If you're seeing this error:</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Make sure you granted all required permissions</li>
                  <li>Check that your Google account is accessible</li>
                  <li>Try the connection process again</li>
                </ul>
              </div>
            )}

            {window.opener && (
              <p className="text-xs text-muted-foreground">
                This window will close automatically.
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OAuthCallback;
