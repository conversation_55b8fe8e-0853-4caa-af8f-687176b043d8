const express = require('express');
const router = express.Router();
const googleAuthService = require('../services/googleAuth');
const GmailService = require('../services/gmailService');
const { supabaseClient } = require('../config/supabase');

/**
 * GET /api/oauth/health
 * Check if OAuth system is properly configured
 */
router.get('/health', async (req, res) => {
  try {
    // Check if the user_oauth_tokens table exists by trying to select from it
    const { data, error } = await supabaseClient
      .from('user_oauth_tokens')
      .select('id')
      .limit(1);

    if (error) {
      return res.status(500).json({
        error: 'Database table not found',
        details: 'Please run the OAuth tokens table migration in Supabase',
        sqlError: error.message
      });
    }

    res.json({
      status: 'healthy',
      message: 'OAuth system is properly configured',
      tableExists: true,
      recordCount: data ? data.length : 0
    });
  } catch (error) {
    console.error('OAuth health check error:', error);
    res.status(500).json({
      error: 'OAuth system configuration error',
      details: error.message
    });
  }
});

/**
 * GET /api/oauth/google/url
 * Get Google OAuth authorization URL
 */
router.get('/google/url', (req, res) => {
  try {
    const authUrl = googleAuthService.getAuthUrl();
    res.json({ authUrl });
  } catch (error) {
    console.error('Error generating auth URL:', error);
    res.status(500).json({ error: 'Failed to generate authorization URL' });
  }
});

/**
 * POST /api/oauth/google/callback
 * Handle Google OAuth callback
 */
router.post('/google/callback', async (req, res) => {
  try {
    const { code, userId } = req.body;

    console.log('OAuth callback received:', {
      codeLength: code ? code.length : 'NO CODE',
      userId: userId ? `${userId.substring(0, 8)}...` : 'NO USER ID',
      bodyKeys: Object.keys(req.body)
    });

    if (!code) {
      console.error('OAuth callback error: No authorization code provided');
      return res.status(400).json({ error: 'Authorization code is required' });
    }

    if (!userId) {
      console.error('OAuth callback error: No user ID provided');
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Exchange code for tokens
    const tokens = await googleAuthService.getTokens(code);
    
    // Get user info from Google
    const userInfo = await googleAuthService.getUserInfo(tokens.access_token);

    // Store tokens in Supabase
    const { data, error } = await supabaseClient
      .from('user_oauth_tokens')
      .upsert({
        user_id: userId,
        provider: 'google',
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        expires_at: new Date(Date.now() + (tokens.expires_in * 1000)),
        scope: tokens.scope,
        google_user_id: userInfo.id,
        google_email: userInfo.email,
        updated_at: new Date()
      }, {
        onConflict: 'user_id,provider'
      });

    if (error) {
      console.error('Error storing OAuth tokens:', error);
      return res.status(500).json({ error: 'Failed to store OAuth tokens' });
    }

    // Test Gmail connection
    const gmailService = new GmailService();
    await gmailService.initializeClient(tokens.access_token, tokens.refresh_token);
    const profile = await gmailService.getProfile();

    res.json({
      success: true,
      message: 'Gmail connected successfully',
      user: {
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture
      },
      gmail: {
        emailAddress: profile.emailAddress,
        messagesTotal: profile.messagesTotal,
        threadsTotal: profile.threadsTotal
      }
    });

  } catch (error) {
    console.error('OAuth callback error:', error);
    res.status(500).json({ 
      error: 'Failed to complete OAuth flow',
      details: error.message 
    });
  }
});

/**
 * GET /api/oauth/google/status/:userId
 * Check Google OAuth connection status
 */
router.get('/google/status/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    const { data, error } = await supabaseClient
      .from('user_oauth_tokens')
      .select('*')
      .eq('user_id', userId)
      .eq('provider', 'google')
      .single();

    if (error || !data) {
      return res.json({ connected: false });
    }

    // Check if token is expired
    const now = new Date();
    const expiresAt = new Date(data.expires_at);

    if (now >= expiresAt) {
      // Try to refresh token
      try {
        const newTokens = await googleAuthService.refreshAccessToken(data.refresh_token);
        
        // Update tokens in database
        await supabaseClient
          .from('user_oauth_tokens')
          .update({
            access_token: newTokens.access_token,
            expires_at: new Date(Date.now() + (newTokens.expires_in * 1000)),
            updated_at: new Date()
          })
          .eq('user_id', userId)
          .eq('provider', 'google');

        return res.json({ 
          connected: true, 
          email: data.google_email,
          refreshed: true 
        });
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        return res.json({ connected: false, expired: true });
      }
    }

    res.json({ 
      connected: true, 
      email: data.google_email,
      expiresAt: data.expires_at 
    });

  } catch (error) {
    console.error('Error checking OAuth status:', error);
    res.status(500).json({ error: 'Failed to check OAuth status' });
  }
});

/**
 * DELETE /api/oauth/google/disconnect/:userId
 * Disconnect Google OAuth
 */
router.delete('/google/disconnect/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    // Get current tokens
    const { data } = await supabaseClient
      .from('user_oauth_tokens')
      .select('access_token')
      .eq('user_id', userId)
      .eq('provider', 'google')
      .single();

    // Revoke tokens with Google
    if (data && data.access_token) {
      await googleAuthService.revokeTokens(data.access_token);
    }

    // Delete tokens from database
    const { error } = await supabaseClient
      .from('user_oauth_tokens')
      .delete()
      .eq('user_id', userId)
      .eq('provider', 'google');

    if (error) {
      console.error('Error deleting OAuth tokens:', error);
      return res.status(500).json({ error: 'Failed to disconnect OAuth' });
    }

    res.json({ success: true, message: 'Gmail disconnected successfully' });

  } catch (error) {
    console.error('Error disconnecting OAuth:', error);
    res.status(500).json({ error: 'Failed to disconnect OAuth' });
  }
});

/**
 * POST /api/oauth/google/refresh/:userId
 * Manually refresh Google OAuth tokens
 */
router.post('/google/refresh/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    const { data, error } = await supabaseClient
      .from('user_oauth_tokens')
      .select('refresh_token')
      .eq('user_id', userId)
      .eq('provider', 'google')
      .single();

    if (error || !data) {
      return res.status(404).json({ error: 'OAuth tokens not found' });
    }

    const newTokens = await googleAuthService.refreshAccessToken(data.refresh_token);
    
    // Update tokens in database
    await supabaseClient
      .from('user_oauth_tokens')
      .update({
        access_token: newTokens.access_token,
        expires_at: new Date(Date.now() + (newTokens.expires_in * 1000)),
        updated_at: new Date()
      })
      .eq('user_id', userId)
      .eq('provider', 'google');

    res.json({ 
      success: true, 
      message: 'Tokens refreshed successfully',
      expiresAt: new Date(Date.now() + (newTokens.expires_in * 1000))
    });

  } catch (error) {
    console.error('Error refreshing tokens:', error);
    res.status(500).json({ error: 'Failed to refresh tokens' });
  }
});

module.exports = router;
