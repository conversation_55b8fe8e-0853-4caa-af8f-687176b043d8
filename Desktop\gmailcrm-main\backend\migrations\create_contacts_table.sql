-- ZiadaCRM Contacts Table Migration
-- Run this in your Supabase SQL editor

-- 1. CONTACTS TABLE
CREATE TABLE IF NOT EXISTS contacts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Basic Information
    first_name VARCHAR(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    mobile VARCHAR(50),
    
    -- Company Information
    company VARCHAR(255),
    job_title VARCHAR(255),
    department VARCHAR(100),
    
    -- Address Information
    address_line1 TEXT,
    address_line2 TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    
    -- Additional Information
    website VARCHAR(255),
    linkedin_url VARCHAR(255),
    notes TEXT,
    tags TEXT[], -- Array of tags
    
    -- Metadata
    source VARCHAR(100) DEFAULT 'manual', -- How contact was acquired (manual, import, etc.)
    status VARCHAR(50) DEFAULT 'active', -- active, inactive, archived
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, email) -- Prevent duplicate emails per user
);

-- 2. CONTACT LISTS TABLE (for marketing campaigns)
CREATE TABLE IF NOT EXISTS contact_lists (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, name)
);

-- 3. CONTACT LIST MEMBERS TABLE
CREATE TABLE IF NOT EXISTS contact_list_members (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    list_id UUID NOT NULL REFERENCES contact_lists(id) ON DELETE CASCADE,
    contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(list_id, contact_id)
);

-- CREATE INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_contacts_user_id ON contacts(user_id);
CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email);
CREATE INDEX IF NOT EXISTS idx_contacts_company ON contacts(company);
CREATE INDEX IF NOT EXISTS idx_contacts_name ON contacts(first_name, last_name);
CREATE INDEX IF NOT EXISTS idx_contacts_status ON contacts(status);
CREATE INDEX IF NOT EXISTS idx_contact_lists_user_id ON contact_lists(user_id);
CREATE INDEX IF NOT EXISTS idx_contact_list_members_list_id ON contact_list_members(list_id);
CREATE INDEX IF NOT EXISTS idx_contact_list_members_contact_id ON contact_list_members(contact_id);

-- ENABLE ROW LEVEL SECURITY
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_lists ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_list_members ENABLE ROW LEVEL SECURITY;

-- CREATE RLS POLICIES
-- Users can only access their own contacts
CREATE POLICY "Users can view their own contacts" ON contacts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own contacts" ON contacts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own contacts" ON contacts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own contacts" ON contacts
    FOR DELETE USING (auth.uid() = user_id);

-- Users can only access their own contact lists
CREATE POLICY "Users can view their own contact lists" ON contact_lists
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own contact lists" ON contact_lists
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own contact lists" ON contact_lists
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own contact lists" ON contact_lists
    FOR DELETE USING (auth.uid() = user_id);

-- Users can only access contact list members for their own lists
CREATE POLICY "Users can view their own contact list members" ON contact_list_members
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM contact_lists 
            WHERE contact_lists.id = contact_list_members.list_id 
            AND contact_lists.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own contact list members" ON contact_list_members
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM contact_lists 
            WHERE contact_lists.id = contact_list_members.list_id 
            AND contact_lists.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own contact list members" ON contact_list_members
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM contact_lists 
            WHERE contact_lists.id = contact_list_members.list_id 
            AND contact_lists.user_id = auth.uid()
        )
    );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at for contacts
CREATE TRIGGER update_contacts_updated_at 
    BEFORE UPDATE ON contacts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default contact lists for new users
CREATE OR REPLACE FUNCTION create_default_contact_lists()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO contact_lists (user_id, name, description) VALUES
    (NEW.id, 'All Contacts', 'Default list containing all contacts'),
    (NEW.id, 'Prospects', 'Potential customers and leads'),
    (NEW.id, 'Customers', 'Active customers and clients'),
    (NEW.id, 'Partners', 'Business partners and vendors');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to create default lists for new users
-- Note: This trigger would be on auth.users table, but we can't modify that in Supabase
-- Instead, we'll create default lists when the user first accesses contacts
