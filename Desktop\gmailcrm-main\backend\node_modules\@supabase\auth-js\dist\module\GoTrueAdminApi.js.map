{"version": 3, "file": "GoTrueAdminApi.js", "sourceRoot": "", "sources": ["../../src/GoTrueAdminApi.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAEL,qBAAqB,EACrB,sBAAsB,EACtB,QAAQ,EACR,aAAa,GACd,MAAM,aAAa,CAAA;AACpB,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAA;AAe5C,OAAO,EAAa,WAAW,EAAE,MAAM,cAAc,CAAA;AAErD,MAAM,CAAC,OAAO,OAAO,cAAc;IAUjC,YAAY,EACV,GAAG,GAAG,EAAE,EACR,OAAO,GAAG,EAAE,EACZ,KAAK,GAON;QACC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAA;QAChC,IAAI,CAAC,GAAG,GAAG;YACT,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;YACzC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;SAC5C,CAAA;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAO,CACX,GAAW,EACX,QAAuC,QAAQ;QAE/C,IAAI;YACF,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,iBAAiB,KAAK,EAAE,EAAE;gBACtE,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,GAAG;gBACH,aAAa,EAAE,IAAI;aACpB,CAAC,CAAA;YACF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;SACnC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;aAC7B;YAED,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,iBAAiB,CACrB,KAAa,EACb,UAMI,EAAE;QAEN,IAAI;YACF,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,SAAS,EAAE;gBAC9D,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE;gBACnC,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,KAAK,EAAE,aAAa;aACrB,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,CAAA;aACvC;YAED,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,YAAY,CAAC,MAA0B;QAC3C,IAAI;YACF,MAAM,EAAE,OAAO,KAAc,MAAM,EAAf,IAAI,UAAK,MAAM,EAA7B,WAAoB,CAAS,CAAA;YACnC,MAAM,IAAI,mCAAa,IAAI,GAAK,OAAO,CAAE,CAAA;YACzC,IAAI,UAAU,IAAI,IAAI,EAAE;gBACtB,kDAAkD;gBAClD,IAAI,CAAC,SAAS,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,CAAA;gBAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,CAAA;aACxB;YACD,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,sBAAsB,EAAE;gBAC3E,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,KAAK,EAAE,qBAAqB;gBAC5B,UAAU,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU;aAChC,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,OAAO;oBACL,IAAI,EAAE;wBACJ,UAAU,EAAE,IAAI;wBAChB,IAAI,EAAE,IAAI;qBACX;oBACD,KAAK;iBACN,CAAA;aACF;YACD,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;IAED,iBAAiB;IACjB;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,UAA+B;QAC9C,IAAI;YACF,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,cAAc,EAAE;gBACnE,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,KAAK,EAAE,aAAa;aACrB,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,CAAA;aACvC;YAED,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,SAAS,CACb,MAAmB;;QAKnB,IAAI;YACF,MAAM,UAAU,GAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;YACxE,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,cAAc,EAAE;gBAC5E,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,aAAa,EAAE,IAAI;gBACnB,KAAK,EAAE;oBACL,IAAI,EAAE,MAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,0CAAE,QAAQ,EAAE,mCAAI,EAAE;oBACpC,QAAQ,EAAE,MAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,0CAAE,QAAQ,EAAE,mCAAI,EAAE;iBAC5C;gBACD,KAAK,EAAE,sBAAsB;aAC9B,CAAC,CAAA;YACF,IAAI,QAAQ,CAAC,KAAK;gBAAE,MAAM,QAAQ,CAAC,KAAK,CAAA;YAExC,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;YACnC,MAAM,KAAK,GAAG,MAAA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,mCAAI,CAAC,CAAA;YACxD,MAAM,KAAK,GAAG,MAAA,MAAA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,0CAAE,KAAK,CAAC,GAAG,CAAC,mCAAI,EAAE,CAAA;YAC5D,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpB,KAAK,CAAC,OAAO,CAAC,CAAC,IAAY,EAAE,EAAE;oBAC7B,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;oBACvE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;oBACxD,UAAU,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,CAAA;gBACjC,CAAC,CAAC,CAAA;gBAEF,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;aACnC;YACD,OAAO,EAAE,IAAI,kCAAO,KAAK,GAAK,UAAU,CAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;SAC1D;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,OAAO,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,CAAA;aACtC;YACD,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,WAAW,CAAC,GAAW;QAC3B,IAAI;YACF,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,gBAAgB,GAAG,EAAE,EAAE;gBACzE,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,KAAK,EAAE,aAAa;aACrB,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,CAAA;aACvC;YAED,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,cAAc,CAAC,GAAW,EAAE,UAA+B;QAC/D,IAAI;YACF,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,gBAAgB,GAAG,EAAE,EAAE;gBACzE,IAAI,EAAE,UAAU;gBAChB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,KAAK,EAAE,aAAa;aACrB,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,CAAA;aACvC;YAED,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,UAAU,CAAC,EAAU,EAAE,gBAAgB,GAAG,KAAK;QACnD,IAAI;YACF,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,gBAAgB,EAAE,EAAE,EAAE;gBAC3E,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,IAAI,EAAE;oBACJ,kBAAkB,EAAE,gBAAgB;iBACrC;gBACD,KAAK,EAAE,aAAa;aACrB,CAAC,CAAA;SACH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,CAAA;aACvC;YAED,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,MAAqC;QAErC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,CACpC,IAAI,CAAC,KAAK,EACV,KAAK,EACL,GAAG,IAAI,CAAC,GAAG,gBAAgB,MAAM,CAAC,MAAM,UAAU,EAClD;gBACE,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,KAAK,EAAE,CAAC,OAAY,EAAE,EAAE;oBACtB,OAAO,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;gBAC3C,CAAC;aACF,CACF,CAAA;YACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;SACvB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;aAC7B;YAED,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,MAAsC;QAEtC,IAAI;YACF,MAAM,IAAI,GAAG,MAAM,QAAQ,CACzB,IAAI,CAAC,KAAK,EACV,QAAQ,EACR,GAAG,IAAI,CAAC,GAAG,gBAAgB,MAAM,CAAC,MAAM,YAAY,MAAM,CAAC,EAAE,EAAE,EAC/D;gBACE,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CACF,CAAA;YAED,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;SAC7B;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;aAC7B;YAED,MAAM,KAAK,CAAA;SACZ;IACH,CAAC;CACF"}