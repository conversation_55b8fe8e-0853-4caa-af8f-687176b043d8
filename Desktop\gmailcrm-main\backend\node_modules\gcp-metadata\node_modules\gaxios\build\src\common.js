"use strict";
// Copyright 2018 Google LLC
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.GaxiosError = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */
class GaxiosError extends Error {
    constructor(message, options, response) {
        super(message);
        this.response = response;
        this.config = options;
        this.response.data = translateData(options.responseType, response.data);
        this.code = response.status.toString();
    }
}
exports.GaxiosError = GaxiosError;
function translateData(responseType, data) {
    switch (responseType) {
        case 'stream':
            return data;
        case 'json':
            return JSON.parse(JSON.stringify(data));
        case 'arraybuffer':
            return JSON.parse(Buffer.from(data).toString('utf8'));
        case 'blob':
            return JSON.parse(data.text());
        default:
            return data;
    }
}
//# sourceMappingURL=common.js.map