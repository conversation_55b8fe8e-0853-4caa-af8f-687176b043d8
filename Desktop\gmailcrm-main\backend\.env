# Server port
PORT=5000

# Supabase configuration
# Get these values from your Supabase project settings
SUPABASE_URL=https://cgkazlrvqoqmbzdwfndh.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.dUb-u96kHa1lSrCDS2MTz06IMFbkFj6snIIKr1ZxVQk
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.ipzxZ8P6ReB_iFIhwZ99p3kOJaN68hVA3w_340KNUlI
# JWT secret key for authentication
# In production, use a strong random string
JWT_SECRET=1DbhmZwL5OqZQXgBO/1EP7GFMhLBjadWdqx5XT2j6Nxs0LWTTtWeBxntGv6QKc4ANky8ugJl4KVpB0PoKPc9+Q==
# Email configuration for password reset
# For Gmail, use an app password: https://support.google.com/accounts/answer/185833
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_app_password

# Frontend URL for links in emails
# Update this to match your frontend URL
CLIENT_URL=http://localhost:8080

# Google OAuth Configuration
# Replace with your actual Google OAuth credentials
GOOGLE_CLIENT_ID=************-ff72eni0ur4off8lddlqak9tvd8hqqcv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
# Gmail API Configuration
GMAIL_SCOPES=https://www.googleapis.com/auth/gmail.readonly,https://www.googleapis.com/auth/gmail.send,https://www.googleapis.com/auth/gmail.modify
